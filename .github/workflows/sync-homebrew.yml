name: sync-homebrew

on:
  workflow_dispatch:
    inputs:
      tag:
        description: 'The git tag name to bump the cask to'
        required: false
        type: string
        default: 'refs/tags/v1.x.x'
  workflow_run:
    workflows: ['release-assets']
    types:
      - completed

jobs:
  sync-homebrew:
    runs-on: macos-latest
    steps:
    - name: Set up Homebrew
      uses: Homebrew/actions/setup-homebrew@master

    - name: Configure Git
      uses: Homebrew/actions/git-user-config@master
      with:
        username: viarotel-bot

    - name: Update Homebrew cask
      env:
        HOMEBREW_GITHUB_API_TOKEN: ${{ secrets.GH_TOKEN }}
      run: |
        # Add the tap
        brew tap viarotel-org/homebrew-escrcpy

        # Get latest release info
        LATEST_RELEASE=$(curl -s "https://api.github.com/repos/viarotel-org/escrcpy/releases/latest")
        VERSION=$(echo "$LATEST_RELEASE" | jq -r '.tag_name' | sed 's/^v//')

        echo "Latest version: $VERSION"

        # Use brew bump-cask-pr to update the cask
        # This will automatically calculate correct sha256 for each architecture
        brew bump-cask-pr \
          --no-browse \
          --no-audit \
          --version="$VERSION" \
          viarotel-org/homebrew-escrcpy/escrcpy